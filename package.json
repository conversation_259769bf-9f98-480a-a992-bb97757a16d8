{"name": "easy-link", "version": "1.0.0", "private": true, "author": "Techtown", "description": "Easy Link", "type": "module", "keywords": ["easy-link"], "scripts": {"dev": "next dev -p 8000 --turbo", "build": "next build", "start": "next start -p 8000", "lint": "next lint", "analyze": "ANALYZE=true next build", "prepare": "husky || true", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@craftjs/core": "^0.2.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@faker-js/faker": "^9.3.0", "@google/generative-ai": "^0.21.0", "@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.1", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@lemonsqueezy/wedges": "^1.4.0", "@meilisearch/instant-meilisearch": "^0.22.0", "@paypal/paypal-js": "^8.1.2", "@paypal/react-paypal-js": "^8.7.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/nextjs": "^9.22.0", "@sentry/react": "^9.22.0", "@stripe/react-stripe-js": "^3.0.0", "@stripe/stripe-js": "^5.2.0", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-dropcursor": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@twicpics/components": "^0.33.0", "@types/locomotive-scroll": "^4.1.4", "appwrite": "^16.1.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.0.0", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dedent": "^1.5.3", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-class-names": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-react": "^8.6.0", "geist": "^1.3.1", "gsap": "^3.13.0", "highlight.js": "^11.11.1", "ioredis": "^5.6.1", "lenis": "^1.2.3", "locomotive-scroll": "^4.1.4", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "motion": "^12.9.2", "next": "15.4.4", "next-intl": "^4.3.4", "next-plausible": "^3.12.4", "next-themes": "^0.4.6", "node-appwrite": "^14.1.0", "openai": "^4.83.0", "pako": "^2.1.0", "pg": "^8.13.1", "pouchdb": "^9.0.0", "qs": "^6.13.1", "query-string": "^9.1.1", "react": "19.1.0", "react-color-palette": "^7.3.0", "react-colorful": "^5.6.1", "react-contenteditable": "^3.3.7", "react-country-flag": "^3.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.4.2", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.0", "react-image-crop": "^11.0.10", "react-intersection-observer": "^9.13.1", "react-locomotive-scroll": "^0.2.2", "react-markdown": "^9.0.1", "react-social-media-embed": "^2.5.18", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "swiper": "^11.2.8", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "webpack": "^5", "zod": "3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.0", "@eslint/js": "^9.16.0", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.13", "@types/node": "^22.15.24", "@types/pako": "^2.0.3", "@types/pg": "^8.11.10", "@types/pouchdb": "^6.4.2", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/react-instantsearch-dom": "^6.12.8", "@types/wicg-file-system-access": "^2023.10.5", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.20", "babel-loader": "^9.2.1", "eslint": "^9.16.0", "eslint-config-next": "15.4.4", "eslint-plugin-react": "^7.37.2", "globals": "^15.13.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "typescript-eslint": "^8.18.0"}, "resolutions": {"webpack": "^5", "rollup": "npm:@rollup/wasm-node"}, "peerDependencies": {"locomotive-scroll": "^4.1.4", "react": "^18.0.2"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "eslint --max-warnings 0 ."]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}