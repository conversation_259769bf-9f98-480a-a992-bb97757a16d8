"use client"

import React, { useEffect, useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"
import { LanguageSelector } from "@/components/ui/language-selector"

interface GeoData {
  country: string
  city: string
  region: string
  timezone: string
  ip: string
  detectedLanguage: string
  detectionMethod: string
  isLocalhost: boolean
  hostname: string
  timestamp: string
}

export function GeoLanguageDebug() {
  const { currentLanguage, language, t } = useLanguage()
  const [geoData, setGeoData] = useState<GeoData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchGeoData = async () => {
      try {
        const response = await fetch("/api/detect-location")
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const data = await response.json()
        setGeoData(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error")
      } finally {
        setIsLoading(false)
      }
    }

    fetchGeoData()
  }, [])

  // Only show in development
  if (process.env.NODE_ENV !== "development") {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm rounded-lg bg-gray-900 p-4 font-mono text-xs text-white shadow-lg">
      <div className="mb-3 flex items-center justify-between">
        <h3 className="text-sm font-bold">🌍 Geo & Language Debug</h3>
        <LanguageSelector variant="compact" className="text-xs" />
      </div>

      <div className="space-y-2">
        <div className="border-b border-gray-700 pb-2">
          <div className="font-semibold text-yellow-400">Current Language:</div>
          <div>Code: {currentLanguage}</div>
          <div>Name: {language.nativeName}</div>
          <div>Flag: {language.flag}</div>
        </div>

        <div className="border-b border-gray-700 pb-2">
          <div className="font-semibold text-blue-400">Translations Test:</div>
          <div>Settings: {t("common", "settings" as const)}</div>
          <div>Home: {t("navigation", "home" as const)}</div>
        </div>

        {isLoading && <div className="text-gray-400">Loading geo data...</div>}

        {error && <div className="text-red-400">Error: {error}</div>}

        {geoData && (
          <div className="space-y-1">
            <div className="font-semibold text-green-400">Geo Detection:</div>
            <div>Method: {geoData.detectionMethod}</div>
            <div>Country: {geoData.country}</div>
            <div>City: {geoData.city}</div>
            <div>Region: {geoData.region}</div>
            <div>Timezone: {geoData.timezone}</div>
            <div>IP: {geoData.ip}</div>
            <div>Detected Lang: {geoData.detectedLanguage}</div>
            <div>Is Localhost: {geoData.isLocalhost ? "Yes" : "No"}</div>
            <div>Hostname: {geoData.hostname}</div>
            <div className="text-[10px] text-gray-400">
              Updated: {new Date(geoData.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}

        <div className="border-t border-gray-700 pt-2">
          <div className="font-semibold text-purple-400">Cookie Info:</div>
          <div>
            Language Cookie:{" "}
            {typeof document !== "undefined"
              ? document.cookie.split("language=")[1]?.split(";")[0] ||
                "Not set"
              : "N/A"}
          </div>
        </div>
      </div>
    </div>
  )
}
