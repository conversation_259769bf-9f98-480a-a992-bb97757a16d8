"use client"

import {
  <PERSON><PERSON><PERSON>,
  BookText,
  Bot,
  CreditCard,
  Crown,
  // Frame,
  GalleryVerticalEnd,
  Gift,
  ListOrderedIcon,
  // Map,
  Newspaper,
  Package2Icon,
  // PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react"
import * as React from "react"

import NavMain from "./nav-main"
import NavUser from "./nav-user"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

import { useAuthContext } from "@/guard/hooks"
import { paths } from "@/routes/paths"
import { cn } from "@/utils"
import Link from "next/link"
import { Logo } from "../logo"
import { Button } from "../ui"
import { useLanguage } from "@/contexts/LanguageContext"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuthContext()
  const { t } = useLanguage()

  // Move data inside component to use translation function
  const data = {
    teams: [
      {
        name: "TechTown Inc",
        logo: GalleryVerticalEnd,
        plan: "Enterprise",
      },
    ],
    navMain: [
      {
        title: t("sidebar", "app" as const),
        url: paths.app.root,
        icon: SquareTerminal,
        isActive: true,
      },
      {
        title: t("sidebar", "linkInBio" as const),
        url: paths.app.me.root,
        icon: Bot,
        items: [
          {
            title: "Link in Bio",
            url: paths.app.me.bio("home"),
          },
          {
            title: t("sidebar", "websites" as const),
            url: paths.app.me.websites,
          },
        ],
      },
      {
        title: t("sidebar", "store" as const),
        url: paths.app.store.root,
        icon: Package2Icon,
        items: [
          {
            title: t("sidebar", "products" as const),
            url: paths.app.store.products,
          },
          {
            title: t("sidebar", "categories" as const),
            url: paths.app.store.categories,
          },
        ],
      },
      {
        title: t("sidebar", "marketing" as const),
        url: paths.app.marketing.email,
        icon: BookOpen,
        items: [
          {
            title: t("sidebar", "emailMarketing" as const),
            url: paths.app.marketing.email,
          },
          {
            title: t("sidebar", "campaigns" as const),
            url: paths.app.marketing.campaign,
          },
        ],
      },
      {
        title: "Blogs",
        url: paths.app.blogs.root,
        icon: Newspaper,
        items: [
          {
            title: "Blogs",
            url: paths.app.blogs.root,
          },
          {
            title: t("sidebar", "categories"),
            url: paths.app.blogs.categoriesBlog,
          },
        ],
      },
      {
        title: "Submissions",
        url: paths.app.submissions.list,
        icon: BookText,
        items: [
          {
            title: "Submissions",
            url: paths.app.submissions.list,
          },
          {
            title: "Forms",
            url: paths.app.form.root,
          },
        ],
      },
    ],
    projects: [
      {
        title: "Orders",
        url: paths.app.store.orders,
        icon: ListOrderedIcon,
      },
    ],
    crefionPro: [
      {
        title: t("sidebar", "crefionPro" as const),
        url: paths.app.crefionPro.root,
        icon: Crown,
      },
      {
        title: "Pricing",
        url: paths.app.crefionPro.pricing,
        icon: Package2Icon,
      },
      {
        title: t("sidebar", "upgrade" as const),
        url: paths.app.crefionPro.payment,
        icon: CreditCard,
      },
    ],
    settings: [
      {
        title: t("sidebar", "settings" as const),
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "Profile",
            url: paths.app.settings.profile,
          },
          {
            title: "Domain",
            url: paths.app.settings.domain,
          },
          {
            title: "Socials",
            url: paths.app.settings.socials,
          },
          {
            title: "Seo",
            url: paths.app.settings.seo,
          },
          {
            title: "Tracking pixels",
            url: paths.app.settings.trackingPixels,
          },
        ],
      },
    ],
    referrals: [
      {
        title: "Referrals",
        url: paths.app.referrals.root,
        icon: Gift,
      },
    ],
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="flex h-16 flex-row items-center justify-between border-b bg-white">
        <div
          className={cn(
            "flex w-full flex-row items-center justify-between lg:gap-4",
            "data-[state=open]:bg-sidebar-accent data-[state=collapsed]:p-0 data-[state=open]:text-sidebar-accent-foreground",
          )}
        >
          <Logo
            src="/images/logo/logo-crefion-primary.png"
            size={100}
            className="mx-auto w-full min-w-8 max-w-20 object-contain"
          />
          <div className="grid flex-1 px-1 text-right text-sm leading-tight">
            <span className="truncate font-semibold">
              {user?.username || "TechTown Inc"}
            </span>
            <span className="truncate text-xs uppercase">
              {user?.team_plan?.name || "FREE"}
            </span>
          </div>
        </div>
      </SidebarHeader>

      <div className="flex flex-col p-2">
        <Link href={paths.app.crefionPro.payment}>
          <Button variant="gradient" className="w-full">
            <Crown className="h-5 w-5 text-white" />
            {t("sidebar", "pro" as const)}
          </Button>
        </Link>
      </div>

      <SidebarContent className="bg-white">
        <NavMain items={data.navMain} />
        <NavMain
          name={t("sidebar", "insight" as const)}
          items={data.projects}
        />
        {/* <NavPopover name="Marketing" items={data.projects} /> */}
        <NavMain
          name={t("sidebar", "crefionPro" as const)}
          items={data.crefionPro}
        />
        <NavMain
          name={t("sidebar", "settings" as const)}
          items={data.settings}
        />
        {/* <NavMain name="Affiliate" items={data.referrals} /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
