"use client"

import Re<PERSON>, { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState } from "react"
import { getCookie, setCookie } from "cookies-next"
import {
  LanguageCode,
  Language,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
  LANGUAGE_COOKIE_NAME,
  LANGUAGE_COOKIE_MAX_AGE,
  TranslationData,
  TranslationKey,
  TranslationNestedKey,
} from "@/lib/language-constants"

interface LanguageContextType {
  currentLanguage: LanguageCode
  language: Language
  isLoading: boolean
  changeLanguage: (languageCode: LanguageCode) => void
  t: (
    key: TranslationKey,
    nestedKey?: TranslationNestedKey<TranslationKey>,
  ) => string
  supportedLanguages: typeof SUPPORTED_LANGUAGES
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined,
)

interface LanguageProviderProps {
  children: React.ReactNode
  initialLanguage?: LanguageCode
}

export function LanguageProvider({
  children,
  initialLanguage,
}: LanguageProviderProps) {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(
    initialLanguage || DEFAULT_LANGUAGE,
  )
  const [isLoading, setIsLoading] = useState(true)
  const [translations, setTranslations] = useState<Record<string, any>>({})

  // Load translations for a specific language
  const loadTranslations = async (languageCode: LanguageCode) => {
    try {
      console.log(`Loading translations for ${languageCode}...`)
      const response = await fetch(`/locales/${languageCode}.json`)
      if (response.ok) {
        const data = await response.json()
        console.log(`Loaded translations for ${languageCode}:`, data)
        setTranslations((prev) => ({ ...prev, [languageCode]: data }))
        return data
      } else {
        console.error(
          `Failed to fetch translations for ${languageCode}: ${response.status}`,
        )
      }
    } catch (error) {
      console.error(`Failed to load translations for ${languageCode}:`, error)
    }
    return null
  }

  useEffect(() => {
    // Get language from cookie or detect from browser/geo
    const detectLanguage = async () => {
      try {
        // Always load default language first
        await loadTranslations(DEFAULT_LANGUAGE)

        // First check cookie
        const savedLanguage = getCookie(LANGUAGE_COOKIE_NAME) as LanguageCode
        if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
          setCurrentLanguage(savedLanguage)
          if (savedLanguage !== DEFAULT_LANGUAGE) {
            await loadTranslations(savedLanguage)
          }
          setIsLoading(false)
          return
        }

        // If no saved language and no initial language, try to detect
        if (!initialLanguage) {
          try {
            const response = await fetch("/api/detect-location")
            const data = await response.json()
            const detectedLanguage = data.detectedLanguage as LanguageCode

            if (SUPPORTED_LANGUAGES[detectedLanguage]) {
              setCurrentLanguage(detectedLanguage)
              await loadTranslations(detectedLanguage)
              // Save to cookie
              setCookie(LANGUAGE_COOKIE_NAME, detectedLanguage, {
                maxAge: LANGUAGE_COOKIE_MAX_AGE,
                path: "/",
                sameSite: "lax",
                secure: process.env.NODE_ENV === "production",
              })
            }
          } catch (error) {
            console.error("Failed to detect language:", error)
            // Fallback to browser language
            const browserLang = navigator.language.split("-")[0] as LanguageCode
            if (SUPPORTED_LANGUAGES[browserLang]) {
              setCurrentLanguage(browserLang)
              await loadTranslations(browserLang)
            }
          }
        }
      } catch (error) {
        console.error("Language detection error:", error)
      } finally {
        setIsLoading(false)
      }
    }

    detectLanguage()
  }, [initialLanguage])

  // Load translations when language changes
  useEffect(() => {
    if (currentLanguage && !translations[currentLanguage]) {
      loadTranslations(currentLanguage)
    }
  }, [currentLanguage, translations])

  const changeLanguage = async (languageCode: LanguageCode) => {
    if (!SUPPORTED_LANGUAGES[languageCode]) {
      console.error(`Unsupported language: ${languageCode}`)
      return
    }

    setCurrentLanguage(languageCode)

    // Load translations for new language if not already loaded
    if (!translations[languageCode]) {
      await loadTranslations(languageCode)
    }

    // Save to cookie
    setCookie(LANGUAGE_COOKIE_NAME, languageCode, {
      maxAge: LANGUAGE_COOKIE_MAX_AGE,
      path: "/",
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
    })

    // Optional: reload page to apply language changes
    // window.location.reload()
  }

  // Translation function
  const t = (
    key: TranslationKey,
    nestedKey?: TranslationNestedKey<TranslationKey>,
  ): string => {
    try {
      console.log(
        `Translation request: ${key}.${nestedKey}, current language: ${currentLanguage}`,
      )
      console.log("Available translations:", Object.keys(translations))

      const currentTranslations =
        translations[currentLanguage] || translations[DEFAULT_LANGUAGE]

      if (!currentTranslations) {
        console.log("No translations found, returning fallback")
        return nestedKey ? `${key}.${nestedKey}` : key
      }

      if (nestedKey) {
        return (
          currentTranslations[key]?.[nestedKey] ||
          translations[DEFAULT_LANGUAGE]?.[key]?.[nestedKey] ||
          `${key}.${nestedKey}`
        )
      }

      return (
        currentTranslations[key] || translations[DEFAULT_LANGUAGE]?.[key] || key
      )
    } catch (error) {
      console.error("Translation error:", error)
      return nestedKey ? `${key}.${nestedKey}` : key
    }
  }

  const language = SUPPORTED_LANGUAGES[currentLanguage]

  const value: LanguageContextType = {
    currentLanguage,
    language,
    isLoading,
    changeLanguage,
    t,
    supportedLanguages: SUPPORTED_LANGUAGES,
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}

// Hook for simple translation without context
export function useTranslation() {
  const { t } = useLanguage()
  return { t }
}
