export const SUPPORTED_LANGUAGES = {
  en: {
    code: "en",
    name: "English",
    nativeName: "English",
    flag: "🇺🇸",
    direction: "ltr",
  },
  vi: {
    code: "vi",
    name: "Vietnamese",
    nativeName: "Tiếng Việt",
    flag: "🇻🇳",
    direction: "ltr",
  },
  ja: {
    code: "ja",
    name: "Japanese",
    nativeName: "日本語",
    flag: "🇯🇵",
    direction: "ltr",
  },
  ko: {
    code: "ko",
    name: "Korean",
    nativeName: "한국어",
    flag: "🇰🇷",
    direction: "ltr",
  },
  zh: {
    code: "zh",
    name: "Chinese",
    nativeName: "中文",
    flag: "🇨🇳",
    direction: "ltr",
  },
  th: {
    code: "th",
    name: "Thai",
    nativeName: "ไทย",
    flag: "🇹🇭",
    direction: "ltr",
  },
  km: {
    code: "km",
    name: "Khmer",
    nativeName: "ខ្មែរ",
    flag: "🇰🇭",
    direction: "ltr",
  },
  fr: {
    code: "fr",
    name: "French",
    nativeName: "Français",
    flag: "🇫🇷",
    direction: "ltr",
  },
  de: {
    code: "de",
    name: "German",
    nativeName: "Deutsch",
    flag: "🇩🇪",
    direction: "ltr",
  },
  es: {
    code: "es",
    name: "Spanish",
    nativeName: "Español",
    flag: "🇪🇸",
    direction: "ltr",
  },
  it: {
    code: "it",
    name: "Italian",
    nativeName: "Italiano",
    flag: "🇮🇹",
    direction: "ltr",
  },
  pt: {
    code: "pt",
    name: "Portuguese",
    nativeName: "Português",
    flag: "🇵🇹",
    direction: "ltr",
  },
  ru: {
    code: "ru",
    name: "Russian",
    nativeName: "Русский",
    flag: "🇷🇺",
    direction: "ltr",
  },
  hi: {
    code: "hi",
    name: "Hindi",
    nativeName: "हिन्दी",
    flag: "🇮🇳",
    direction: "ltr",
  },
  id: {
    code: "id",
    name: "Indonesian",
    nativeName: "Bahasa Indonesia",
    flag: "🇮🇩",
    direction: "ltr",
  },
  ms: {
    code: "ms",
    name: "Malay",
    nativeName: "Bahasa Melayu",
    flag: "🇲🇾",
    direction: "ltr",
  },
} as const

export type LanguageCode = keyof typeof SUPPORTED_LANGUAGES
export type Language = (typeof SUPPORTED_LANGUAGES)[LanguageCode]

export const DEFAULT_LANGUAGE: LanguageCode = "en"

export const LANGUAGE_COOKIE_NAME = "language"
export const LANGUAGE_COOKIE_MAX_AGE = 60 * 60 * 24 * 30 // 30 days

// Basic translations - can be extended later
export const TRANSLATIONS = {
  en: {
    common: {
      language: "Language",
      selectLanguage: "Select Language",
      settings: "Settings",
      save: "Save",
      cancel: "Cancel",
      loading: "Loading...",
      error: "Error",
      success: "Success",
    },
    navigation: {
      home: "Home",
      about: "About",
      contact: "Contact",
      login: "Login",
      register: "Register",
      dashboard: "Dashboard",
    },
  },
  vi: {
    common: {
      language: "Ngôn ngữ",
      selectLanguage: "Chọn ngôn ngữ",
      settings: "Cài đặt",
      save: "Lưu",
      cancel: "Hủy",
      loading: "Đang tải...",
      error: "Lỗi",
      success: "Thành công",
    },
    navigation: {
      home: "Trang chủ",
      about: "Giới thiệu",
      contact: "Liên hệ",
      login: "Đăng nhập",
      register: "Đăng ký",
      dashboard: "Bảng điều khiển",
    },
  },
} as const

export type TranslationKey = keyof (typeof TRANSLATIONS)["en"]
export type TranslationNestedKey<T extends TranslationKey> =
  keyof (typeof TRANSLATIONS)["en"][T]
